module com.logictrue {
    requires javafx.controls;
    requires javafx.fxml;
    requires org.slf4j;
    requires ch.qos.logback.classic;
    requires ch.qos.logback.core;
    requires java.net.http;
    requires java.desktop;
    requires java.sql;
    requires org.apache.commons.collections4;

    // MyBatis-Plus相关依赖
    requires mybatis.plus.core;
    requires mybatis.plus.annotation;
    requires org.mybatis;
    requires com.zaxxer.hikari;
    requires com.alibaba.fastjson2;
    requires org.xerial.sqlitejdbc;
    requires org.apache.poi.poi;
    requires org.apache.poi.ooxml;

    // 导出包
    exports com.logictrue;
    exports com.logictrue.controller;
    exports com.logictrue.config;
    exports com.logictrue.service;
    exports com.logictrue.model;
    exports com.logictrue.mapper;

    // 开放包给JavaFX FXML进行反射访问
    opens com.logictrue to javafx.fxml;
    opens com.logictrue.controller to javafx.fxml;

    // 开放包给fastjson2进行JSON序列化/反序列化
    opens com.logictrue.config to com.alibaba.fastjson2;
    opens com.logictrue.model to com.alibaba.fastjson2;

    // 开放包给MyBatis-Plus进行反射访问
    opens com.logictrue.mapper to mybatis.plus.core, org.mybatis;
}
