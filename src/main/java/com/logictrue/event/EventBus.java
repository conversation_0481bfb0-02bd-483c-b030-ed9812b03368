package com.logictrue.event;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;

/**
 * 简单的事件总线实现
 * 用于应用内部组件间的事件通信
 */
public class EventBus {
    private static final Logger logger = LoggerFactory.getLogger(EventBus.class);
    private static EventBus instance;
    
    private final CopyOnWriteArrayList<Consumer<BackgroundImageUpdateEvent>> backgroundImageUpdateListeners;

    private EventBus() {
        this.backgroundImageUpdateListeners = new CopyOnWriteArrayList<>();
    }

    public static synchronized EventBus getInstance() {
        if (instance == null) {
            instance = new EventBus();
        }
        return instance;
    }

    /**
     * 注册背景图片更新事件监听器
     */
    public void registerBackgroundImageUpdateListener(Consumer<BackgroundImageUpdateEvent> listener) {
        if (listener != null) {
            backgroundImageUpdateListeners.add(listener);
            logger.debug("注册背景图片更新监听器，当前监听器数量: {}", backgroundImageUpdateListeners.size());
        }
    }

    /**
     * 取消注册背景图片更新事件监听器
     */
    public void unregisterBackgroundImageUpdateListener(Consumer<BackgroundImageUpdateEvent> listener) {
        if (listener != null) {
            backgroundImageUpdateListeners.remove(listener);
            logger.debug("取消注册背景图片更新监听器，当前监听器数量: {}", backgroundImageUpdateListeners.size());
        }
    }

    /**
     * 发布背景图片更新事件
     */
    public void publishBackgroundImageUpdateEvent(BackgroundImageUpdateEvent event) {
        if (event == null) {
            return;
        }

        logger.info("发布背景图片更新事件: {}", event);
        
        for (Consumer<BackgroundImageUpdateEvent> listener : backgroundImageUpdateListeners) {
            try {
                listener.accept(event);
            } catch (Exception e) {
                logger.error("处理背景图片更新事件时发生异常", e);
            }
        }
    }

    /**
     * 发布背景图片更新事件（便捷方法）
     */
    public void publishBackgroundImageUpdate(String imagePath) {
        publishBackgroundImageUpdateEvent(new BackgroundImageUpdateEvent(imagePath));
    }

    /**
     * 获取当前背景图片更新监听器数量
     */
    public int getBackgroundImageUpdateListenerCount() {
        return backgroundImageUpdateListeners.size();
    }
}
