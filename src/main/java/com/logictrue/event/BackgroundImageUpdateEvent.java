package com.logictrue.event;

/**
 * 背景图片更新事件
 * 用于通知主页控制器更新背景图片
 */
public class BackgroundImageUpdateEvent {
    private final String imagePath;
    private final long timestamp;

    public BackgroundImageUpdateEvent(String imagePath) {
        this.imagePath = imagePath;
        this.timestamp = System.currentTimeMillis();
    }

    public String getImagePath() {
        return imagePath;
    }

    public long getTimestamp() {
        return timestamp;
    }

    @Override
    public String toString() {
        return "BackgroundImageUpdateEvent{" +
                "imagePath='" + imagePath + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
