package com.logictrue.service;

import com.logictrue.config.MyBatisPlusConfig;
import com.logictrue.iot.entity.DeviceDetectionBasicField;
import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.iot.entity.DeviceDetectionTableData;
import com.logictrue.iot.entity.DeviceDetectionTableHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 动态表创建服务
 * 根据实体类的MyBatis Plus注解动态创建数据库表
 */
public class DynamicTableCreationService {
    private static final Logger logger = LoggerFactory.getLogger(DynamicTableCreationService.class);

    private static DynamicTableCreationService instance;
    private MyBatisPlusConfig myBatisPlusConfig;

    // 需要创建的实体类列表
    private static final List<Class<?>> ENTITY_CLASSES = Arrays.asList(
        DeviceDetectionData.class,
        DeviceDetectionBasicField.class,
        DeviceDetectionTableHeader.class,
        DeviceDetectionTableData.class
    );

    private DynamicTableCreationService() {
        // 延迟初始化，避免循环依赖
    }

    /**
     * 初始化服务（延迟初始化）
     */
    public void initialize() {
        if (this.myBatisPlusConfig == null) {
            this.myBatisPlusConfig = MyBatisPlusConfig.getInstance();
            initializeTables();
        }
    }

    /**
     * 确保服务已初始化
     */
    private void ensureInitialized() {
        if (this.myBatisPlusConfig == null) {
            initialize();
        }
    }

    /**
     * 获取单例实例
     */
    public static synchronized DynamicTableCreationService getInstance() {
        if (instance == null) {
            instance = new DynamicTableCreationService();
        }
        return instance;
    }

    /**
     * 初始化所有表
     */
    private void initializeTables() {
        logger.info("开始动态创建数据库表结构");

        try (Connection conn = myBatisPlusConfig.getDataSource().getConnection()) {
            for (Class<?> entityClass : ENTITY_CLASSES) {
                createTableFromEntity(conn, entityClass);
            }
            logger.info("动态表创建完成，共处理 {} 个实体类", ENTITY_CLASSES.size());
        } catch (Exception e) {
            logger.error("动态表创建失败", e);
            throw new RuntimeException("动态表创建失败", e);
        }
    }

    /**
     * 根据实体类创建表
     */
    private void createTableFromEntity(Connection conn, Class<?> entityClass) {
        try {
            logger.info("开始处理实体类: {}", entityClass.getName());

            // 解析实体类注解
            EntityAnnotationParser.TableInfo tableInfo = EntityAnnotationParser.parseEntity(entityClass);
            String tableName = tableInfo.getTableName();

            // 检查表是否已存在
            if (tableExists(conn, tableName)) {
                logger.info("表 {} 已存在，跳过创建", tableName);
                // 可以在这里添加表结构比较和更新逻辑
                return;
            }

            // 生成并执行CREATE TABLE语句
            String createTableSql = DynamicSqlGenerator.generateCreateTableSql(tableInfo);
            executeSQL(conn, createTableSql);
            logger.info("表 {} 创建成功", tableName);

            // 创建索引
            List<String> indexSqls = DynamicSqlGenerator.generateIndexSql(tableInfo);
            for (String indexSql : indexSqls) {
                executeSQL(conn, indexSql);
            }

            if (!indexSqls.isEmpty()) {
                logger.info("为表 {} 创建了 {} 个索引", tableName, indexSqls.size());
            }

        } catch (Exception e) {
            logger.error("处理实体类 {} 时发生错误", entityClass.getName(), e);
            throw new RuntimeException("创建表失败: " + entityClass.getName(), e);
        }
    }

    /**
     * 检查表是否存在
     */
    private boolean tableExists(Connection conn, String tableName) throws SQLException {
        String sql = DynamicSqlGenerator.generateTableExistsSql(tableName);
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            return rs.next();
        }
    }

    /**
     * 执行SQL语句
     */
    private void executeSQL(Connection conn, String sql) throws SQLException {
        logger.debug("执行SQL: {}", sql);
        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
        }
    }

    /**
     * 获取表结构信息
     */
    public List<TableColumnInfo> getTableStructure(String tableName) {
        ensureInitialized();
        List<TableColumnInfo> columns = new ArrayList<>();

        try (Connection conn = myBatisPlusConfig.getDataSource().getConnection()) {
            String sql = DynamicSqlGenerator.generateTableInfoSql(tableName);
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {

                while (rs.next()) {
                    TableColumnInfo column = new TableColumnInfo();
                    column.setColumnId(rs.getInt("cid"));
                    column.setColumnName(rs.getString("name"));
                    column.setDataType(rs.getString("type"));
                    column.setNotNull(rs.getBoolean("notnull"));
                    column.setDefaultValue(rs.getString("dflt_value"));
                    column.setPrimaryKey(rs.getBoolean("pk"));
                    columns.add(column);
                }
            }
        } catch (SQLException e) {
            logger.error("获取表结构信息失败: {}", tableName, e);
        }

        return columns;
    }

    /**
     * 比较实体类与数据库表结构
     */
    public TableComparisonResult compareTableStructure(Class<?> entityClass) {
        EntityAnnotationParser.TableInfo entityTableInfo = EntityAnnotationParser.parseEntity(entityClass);
        String tableName = entityTableInfo.getTableName();

        List<TableColumnInfo> dbColumns = getTableStructure(tableName);
        TableComparisonResult result = new TableComparisonResult();
        result.setTableName(tableName);
        result.setEntityClass(entityClass);

        // 比较逻辑
        List<EntityAnnotationParser.ColumnInfo> missingColumns = new ArrayList<>();
        List<EntityAnnotationParser.ColumnInfo> differentColumns = new ArrayList<>();

        for (EntityAnnotationParser.ColumnInfo entityColumn : entityTableInfo.getColumns()) {
            boolean found = false;
            for (TableColumnInfo dbColumn : dbColumns) {
                if (entityColumn.getColumnName().equals(dbColumn.getColumnName())) {
                    found = true;
                    // 检查数据类型是否一致
                    if (!isDataTypeCompatible(entityColumn.getDataType(), dbColumn.getDataType())) {
                        differentColumns.add(entityColumn);
                    }
                    break;
                }
            }
            if (!found) {
                missingColumns.add(entityColumn);
            }
        }

        result.setMissingColumns(missingColumns);
        result.setDifferentColumns(differentColumns);
        result.setNeedsUpdate(!missingColumns.isEmpty() || !differentColumns.isEmpty());

        return result;
    }

    /**
     * 检查数据类型是否兼容
     */
    private boolean isDataTypeCompatible(String entityType, String dbType) {
        // 简化的类型兼容性检查
        if (entityType.equals(dbType)) {
            return true;
        }

        // SQLite的类型兼容性
        if ("INTEGER".equals(entityType) && dbType.toUpperCase().contains("INT")) {
            return true;
        }
        if ("TEXT".equals(entityType) && (dbType.toUpperCase().contains("TEXT") ||
                                         dbType.toUpperCase().contains("VARCHAR") ||
                                         dbType.toUpperCase().contains("CHAR"))) {
            return true;
        }

        return false;
    }

    /**
     * 更新表结构
     */
    public boolean updateTableStructure(Class<?> entityClass) {
        ensureInitialized();
        try {
            TableComparisonResult comparison = compareTableStructure(entityClass);
            if (!comparison.isNeedsUpdate()) {
                logger.info("表 {} 结构无需更新", comparison.getTableName());
                return true;
            }

            try (Connection conn = myBatisPlusConfig.getDataSource().getConnection()) {
                // 添加缺失的列
                if (!comparison.getMissingColumns().isEmpty()) {
                    List<String> alterSqls = DynamicSqlGenerator.generateAlterTableSql(
                        comparison.getTableName(), comparison.getMissingColumns());

                    for (String alterSql : alterSqls) {
                        executeSQL(conn, alterSql);
                    }

                    logger.info("为表 {} 添加了 {} 个缺失的列",
                        comparison.getTableName(), comparison.getMissingColumns().size());
                }

                // 注意：SQLite不支持修改列类型，如果需要修改列类型，需要重建表
                if (!comparison.getDifferentColumns().isEmpty()) {
                    logger.warn("表 {} 存在类型不匹配的列，SQLite不支持直接修改列类型: {}",
                        comparison.getTableName(),
                        comparison.getDifferentColumns().stream()
                            .map(EntityAnnotationParser.ColumnInfo::getColumnName)
                            .toArray());
                }

                return true;
            }
        } catch (Exception e) {
            logger.error("更新表结构失败: {}", entityClass.getName(), e);
            return false;
        }
    }

    /**
     * 重新创建所有表
     */
    public void recreateAllTables() {
        ensureInitialized();
        logger.warn("开始重新创建所有表");

        try (Connection conn = myBatisPlusConfig.getDataSource().getConnection()) {
            for (Class<?> entityClass : ENTITY_CLASSES) {
                EntityAnnotationParser.TableInfo tableInfo = EntityAnnotationParser.parseEntity(entityClass);
                String tableName = tableInfo.getTableName();

                // 删除现有表
                String dropSql = DynamicSqlGenerator.generateDropTableSql(tableName);
                executeSQL(conn, dropSql);
                logger.info("删除表: {}", tableName);

                // 重新创建表
                createTableFromEntity(conn, entityClass);
            }

            logger.info("所有表重新创建完成");
        } catch (Exception e) {
            logger.error("重新创建表失败", e);
            throw new RuntimeException("重新创建表失败", e);
        }
    }

    /**
     * 获取MyBatis Plus配置
     */
    public MyBatisPlusConfig getMyBatisPlusConfig() {
        return myBatisPlusConfig;
    }

    // 内部类定义
    public static class TableColumnInfo {
        private int columnId;
        private String columnName;
        private String dataType;
        private boolean notNull;
        private String defaultValue;
        private boolean primaryKey;

        // Getters and Setters
        public int getColumnId() { return columnId; }
        public void setColumnId(int columnId) { this.columnId = columnId; }

        public String getColumnName() { return columnName; }
        public void setColumnName(String columnName) { this.columnName = columnName; }

        public String getDataType() { return dataType; }
        public void setDataType(String dataType) { this.dataType = dataType; }

        public boolean isNotNull() { return notNull; }
        public void setNotNull(boolean notNull) { this.notNull = notNull; }

        public String getDefaultValue() { return defaultValue; }
        public void setDefaultValue(String defaultValue) { this.defaultValue = defaultValue; }

        public boolean isPrimaryKey() { return primaryKey; }
        public void setPrimaryKey(boolean primaryKey) { this.primaryKey = primaryKey; }
    }

    public static class TableComparisonResult {
        private String tableName;
        private Class<?> entityClass;
        private List<EntityAnnotationParser.ColumnInfo> missingColumns;
        private List<EntityAnnotationParser.ColumnInfo> differentColumns;
        private boolean needsUpdate;

        // Getters and Setters
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }

        public Class<?> getEntityClass() { return entityClass; }
        public void setEntityClass(Class<?> entityClass) { this.entityClass = entityClass; }

        public List<EntityAnnotationParser.ColumnInfo> getMissingColumns() { return missingColumns; }
        public void setMissingColumns(List<EntityAnnotationParser.ColumnInfo> missingColumns) {
            this.missingColumns = missingColumns;
        }

        public List<EntityAnnotationParser.ColumnInfo> getDifferentColumns() { return differentColumns; }
        public void setDifferentColumns(List<EntityAnnotationParser.ColumnInfo> differentColumns) {
            this.differentColumns = differentColumns;
        }

        public boolean isNeedsUpdate() { return needsUpdate; }
        public void setNeedsUpdate(boolean needsUpdate) { this.needsUpdate = needsUpdate; }
    }
}
