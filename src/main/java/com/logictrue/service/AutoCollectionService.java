package com.logictrue.service;

import com.logictrue.config.ConfigManager;
import com.logictrue.service.DataCollectionService.DataCollectionResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.CompletableFuture;

/**
 * 自动采集服务
 * 负责在心跳检测成功后执行自动采集任务
 */
public class AutoCollectionService {
    private static final Logger logger = LoggerFactory.getLogger(AutoCollectionService.class);

    private ConfigManager configManager;
    private DataCollectionService dataCollectionService;

    public AutoCollectionService() {
        this.configManager = ConfigManager.getInstance();
        this.dataCollectionService = new DataCollectionService();
    }

    /**
     * 执行自动采集任务
     * 在心跳成功后调用此方法
     */
    public CompletableFuture<AutoCollectionResult> performAutoCollection() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("开始检查自动采集条件");

                // 1. 检查是否启用自动采集
                if (!configManager.isAutoExcelCollection()) {
                    logger.debug("自动采集功能已禁用，跳过采集");
                    return AutoCollectionResult.success(0, "自动采集功能已禁用");
                }

                // 2. 检查是否超过采集间隔
                if (!isCollectionIntervalReached()) {
                    logger.debug("未到采集间隔时间，跳过采集");
                    return AutoCollectionResult.success(0, "未到采集间隔时间");
                }

                // 3. 检查采集路径配置
                String excelCollectionPath = configManager.getExcelCollectionPath();
                if (excelCollectionPath == null || excelCollectionPath.trim().isEmpty()) {
                    logger.warn("Excel采集路径未配置，跳过自动采集");
                    return AutoCollectionResult.failure("Excel采集路径未配置");
                }

                logger.info("开始执行自动采集任务，采集路径: {}", excelCollectionPath);

                // 4. 执行自动采集（不需要表单数据，使用空的表单控件映射）
                DataCollectionResult collectionResult = dataCollectionService.performAutoCollection();

                // 5. 更新最后采集时间
                updateLastCollectionTime();

                if (collectionResult.isSuccess()) {
                    logger.info("自动采集任务完成，成功处理{}个文件", collectionResult.getProcessedCount());
                    return AutoCollectionResult.success(collectionResult.getProcessedCount(),
                            "自动采集完成，处理了" + collectionResult.getProcessedCount() + "个文件");
                } else {
                    logger.warn("自动采集任务失败: {}", collectionResult.getErrorMessage());
                    return AutoCollectionResult.failure("自动采集失败: " + collectionResult.getErrorMessage());
                }

            } catch (Exception e) {
                logger.error("自动采集任务异常", e);
                return AutoCollectionResult.failure("自动采集异常: " + e.getMessage());
            }
        });
    }

    /**
     * 检查是否到达采集间隔时间
     */
    private boolean isCollectionIntervalReached() {
        try {
            String lastCollectionTime = configManager.getLastAutoCollectionTime();

            // 如果没有上次采集时间记录，则认为需要采集
            if (lastCollectionTime == null || lastCollectionTime.trim().isEmpty()) {
                logger.info("首次自动采集，无上次采集时间记录");
                return true;
            }

            // 解析上次采集时间
            LocalDateTime lastTime = LocalDateTime.parse(lastCollectionTime);
            LocalDateTime currentTime = LocalDateTime.now();

            // 计算时间差（分钟）
            long minutesSinceLastCollection = ChronoUnit.MINUTES.between(lastTime, currentTime);
            int configuredInterval = configManager.getCollectionIntervalMinutes();

            logger.debug("上次采集时间: {}, 当前时间: {}, 间隔: {}分钟, 配置间隔: {}分钟",
                    lastTime, currentTime, minutesSinceLastCollection, configuredInterval);

            boolean shouldCollect = minutesSinceLastCollection >= configuredInterval;
            if (shouldCollect) {
                logger.info("已超过采集间隔时间，开始执行自动采集");
            } else {
                logger.debug("距离下次采集还有{}分钟", configuredInterval - minutesSinceLastCollection);
            }

            return shouldCollect;

        } catch (Exception e) {
            logger.error("检查采集间隔时间异常", e);
            // 异常情况下，为了保证系统正常运行，返回true执行采集
            return true;
        }
    }

    /**
     * 更新最后采集时间
     */
    private void updateLastCollectionTime() {
        try {
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            configManager.setLastAutoCollectionTime(currentTime);
            logger.info("更新最后自动采集时间: {}", currentTime);
        } catch (Exception e) {
            logger.error("更新最后采集时间失败", e);
        }
    }

    /**
     * 自动采集结果类
     */
    public static class AutoCollectionResult {
        private boolean success;
        private int processedCount;
        private String message;

        private AutoCollectionResult(boolean success, int processedCount, String message) {
            this.success = success;
            this.processedCount = processedCount;
            this.message = message;
        }

        public static AutoCollectionResult success(int processedCount, String message) {
            return new AutoCollectionResult(true, processedCount, message);
        }

        public static AutoCollectionResult failure(String message) {
            return new AutoCollectionResult(false, 0, message);
        }

        public boolean isSuccess() {
            return success;
        }

        public int getProcessedCount() {
            return processedCount;
        }

        public String getMessage() {
            return message;
        }
    }
}
