package com.logictrue.service;

import com.logictrue.model.FormField;
import javafx.scene.control.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 表单数据缓存服务（内存版本）
 * 负责在应用运行期间保存和恢复动态表单的填写内容
 */
public class FormDataCacheService {
    private static final Logger logger = LoggerFactory.getLogger(FormDataCacheService.class);

    private static FormDataCacheService instance;

    // 内存缓存
    private Map<String, Object> cachedFormData = new HashMap<>();
    private String cacheTime;

    private FormDataCacheService() {
        // 私有构造函数
    }

    /**
     * 获取单例实例
     */
    public static synchronized FormDataCacheService getInstance() {
        if (instance == null) {
            instance = new FormDataCacheService();
        }
        return instance;
    }

    /**
     * 保存表单数据到内存缓存
     */
    public void saveFormData(Map<String, Control> fieldControls, List<FormField> formFields) {
        try {
            Map<String, Object> formData = new HashMap<>();

            // 收集表单数据
            for (FormField field : formFields) {
                Control control = fieldControls.get(field.getName());
                if (control != null) {
                    Object value = getControlValue(control);
                    if (value != null && !value.toString().trim().isEmpty()) {
                        formData.put(field.getName(), value);
                    }
                }
            }

            // 更新内存缓存
            this.cachedFormData = formData;
            this.cacheTime = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);

            logger.debug("表单数据已保存到内存缓存，字段数: {}", formData.size());

        } catch (Exception e) {
            logger.error("保存表单数据到缓存失败", e);
        }
    }

    /**
     * 从内存缓存恢复表单数据
     */
    public void restoreFormData(Map<String, Control> fieldControls, List<FormField> formFields) {
        try {
            if (cachedFormData.isEmpty()) {
                logger.debug("没有找到缓存的表单数据");
                return;
            }

            int restoredCount = 0;

            // 恢复表单数据
            for (FormField field : formFields) {
                Control control = fieldControls.get(field.getName());
                if (control != null && cachedFormData.containsKey(field.getName())) {
                    Object cachedValue = cachedFormData.get(field.getName());
                    if (setControlValue(control, cachedValue, field.getType())) {
                        restoredCount++;
                    }
                }
            }

            logger.info("表单数据恢复完成，恢复字段数: {}", restoredCount);

        } catch (Exception e) {
            logger.error("从缓存恢复表单数据失败", e);
        }
    }

    /**
     * 获取控件的值
     */
    private Object getControlValue(Control control) {
        if (control instanceof TextField) {
            return ((TextField) control).getText();
        } else if (control instanceof TextArea) {
            return ((TextArea) control).getText();
        } else if (control instanceof DatePicker) {
            LocalDate date = ((DatePicker) control).getValue();
            return date != null ? date.toString() : null;
        } else if (control instanceof ComboBox) {
            return ((ComboBox<?>) control).getValue();
        } else if (control instanceof CheckBox) {
            return ((CheckBox) control).isSelected();
        }
        return null;
    }

    /**
     * 设置控件的值
     */
    private boolean setControlValue(Control control, Object value, FormField.FieldType fieldType) {
        try {
            if (value == null) {
                return false;
            }

            String stringValue = value.toString();

            if (control instanceof TextField) {
                ((TextField) control).setText(stringValue);
                return true;
            } else if (control instanceof TextArea) {
                ((TextArea) control).setText(stringValue);
                return true;
            } else if (control instanceof DatePicker) {
                try {
                    LocalDate date = LocalDate.parse(stringValue);
                    ((DatePicker) control).setValue(date);
                    return true;
                } catch (Exception e) {
                    logger.warn("日期格式解析失败: {}", stringValue);
                }
            } else if (control instanceof ComboBox) {
                @SuppressWarnings("unchecked")
                ComboBox<Object> comboBox = (ComboBox<Object>) control;
                comboBox.setValue(value);
                return true;
            } else if (control instanceof CheckBox) {
                if (value instanceof Boolean) {
                    ((CheckBox) control).setSelected((Boolean) value);
                    return true;
                } else {
                    ((CheckBox) control).setSelected(Boolean.parseBoolean(stringValue));
                    return true;
                }
            }

        } catch (Exception e) {
            logger.warn("设置控件值失败: {}", e.getMessage());
        }

        return false;
    }

    /**
     * 检查是否有缓存数据
     */
    public boolean hasCachedData() {
        return !cachedFormData.isEmpty();
    }

    /**
     * 获取缓存时间
     */
    public String getCacheTime() {
        return cacheTime;
    }

    /**
     * 获取缓存的字段数量
     */
    public int getCachedFieldCount() {
        return cachedFormData.size();
    }

    /**
     * 实时保存表单数据（在用户输入时调用）
     */
    public void autoSaveFormData(Map<String, Control> fieldControls, List<FormField> formFields) {
        // 延迟保存，避免频繁操作
        new Thread(() -> {
            try {
                Thread.sleep(500); // 延迟0.5秒
                saveFormData(fieldControls, formFields);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }
}
