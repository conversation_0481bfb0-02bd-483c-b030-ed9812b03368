package com.logictrue.service;

import com.logictrue.model.ExternalApp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 外部应用程序启动服务
 */
public class ExternalAppService {
    private static final Logger logger = LoggerFactory.getLogger(ExternalAppService.class);
    
    /**
     * 启动外部应用程序
     */
    public CompletableFuture<Boolean> launchApp(ExternalApp app) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("启动外部应用程序: {}", app.getName());
                
                // 验证应用程序路径
                File appFile = new File(app.getPath());
                if (!appFile.exists()) {
                    logger.error("应用程序文件不存在: {}", app.getPath());
                    return false;
                }
                
                // 构建命令
                List<String> command = new ArrayList<>();
                command.add(app.getPath());
                
                // 添加启动参数
                if (app.getArguments() != null && !app.getArguments().trim().isEmpty()) {
                    String[] args = app.getArguments().trim().split("\\s+");
                    for (String arg : args) {
                        command.add(arg);
                    }
                }
                
                // 创建进程构建器
                ProcessBuilder processBuilder = new ProcessBuilder(command);
                
                // 设置工作目录
                if (app.getWorkingDir() != null && !app.getWorkingDir().trim().isEmpty()) {
                    File workingDir = new File(app.getWorkingDir());
                    if (workingDir.exists() && workingDir.isDirectory()) {
                        processBuilder.directory(workingDir);
                    } else {
                        logger.warn("工作目录不存在，使用默认目录: {}", app.getWorkingDir());
                    }
                } else {
                    // 使用应用程序所在目录作为工作目录
                    processBuilder.directory(appFile.getParentFile());
                }
                
                // 启动进程
                Process process = processBuilder.start();
                
                logger.info("外部应用程序启动成功: {} (PID: {})", app.getName(), process.pid());
                return true;
                
            } catch (IOException e) {
                logger.error("启动外部应用程序失败: " + app.getName(), e);
                return false;
            } catch (Exception e) {
                logger.error("启动外部应用程序异常: " + app.getName(), e);
                return false;
            }
        });
    }
    
    /**
     * 验证应用程序路径是否有效
     */
    public boolean validateAppPath(String path) {
        if (path == null || path.trim().isEmpty()) {
            return false;
        }
        
        File appFile = new File(path);
        return appFile.exists() && appFile.isFile() && appFile.canExecute();
    }
    
    /**
     * 获取应用程序信息
     */
    public String getAppInfo(String path) {
        try {
            File appFile = new File(path);
            if (!appFile.exists()) {
                return "文件不存在";
            }
            
            StringBuilder info = new StringBuilder();
            info.append("文件名: ").append(appFile.getName()).append("\n");
            info.append("路径: ").append(appFile.getAbsolutePath()).append("\n");
            info.append("大小: ").append(formatFileSize(appFile.length())).append("\n");
            info.append("可执行: ").append(appFile.canExecute() ? "是" : "否").append("\n");
            info.append("最后修改: ").append(new java.util.Date(appFile.lastModified()));
            
            return info.toString();
        } catch (Exception e) {
            logger.error("获取应用程序信息失败", e);
            return "获取信息失败: " + e.getMessage();
        }
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 检查应用程序是否正在运行
     */
    public boolean isAppRunning(String appName) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            ProcessBuilder processBuilder;
            
            if (os.contains("win")) {
                processBuilder = new ProcessBuilder("tasklist", "/FI", "IMAGENAME eq " + appName);
            } else {
                processBuilder = new ProcessBuilder("pgrep", "-f", appName);
            }
            
            Process process = processBuilder.start();
            int exitCode = process.waitFor();
            
            return exitCode == 0;
        } catch (Exception e) {
            logger.error("检查应用程序运行状态失败", e);
            return false;
        }
    }
}
