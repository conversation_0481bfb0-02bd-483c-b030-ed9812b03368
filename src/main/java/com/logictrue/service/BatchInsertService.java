package com.logictrue.service;

import com.logictrue.config.MyBatisPlusConfig;
import com.logictrue.iot.entity.DeviceDetectionBasicField;
import com.logictrue.iot.entity.DeviceDetectionTableData;
import com.logictrue.iot.entity.DeviceDetectionTableHeader;
import com.logictrue.mapper.DeviceDetectionBasicFieldMapper;
import com.logictrue.mapper.DeviceDetectionTableDataMapper;
import com.logictrue.mapper.DeviceDetectionTableHeaderMapper;
import com.logictrue.util.BatchInsertUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;

/**
 * 批量插入服务类
 * 提供统一的批量插入功能，直接使用Mapper进行数据库操作
 */
public class BatchInsertService {

    private static final Logger logger = LoggerFactory.getLogger(BatchInsertService.class);

    // 批次大小常量，可根据实际情况调整
    private static final int BATCH_SIZE = 500;
    // 连接超时重试次数
    private static final int MAX_RETRY_TIMES = 3;

    private final MyBatisPlusConfig myBatisPlusConfig;

    // 单例模式
    private static volatile BatchInsertService instance;

    private BatchInsertService() {
        // 初始化MyBatis-Plus配置
        this.myBatisPlusConfig = MyBatisPlusConfig.getInstance();
        logger.info("BatchInsertService初始化完成");
    }

    /**
     * 获取单例实例
     */
    public static BatchInsertService getInstance() {
        if (instance == null) {
            synchronized (BatchInsertService.class) {
                if (instance == null) {
                    instance = new BatchInsertService();
                }
            }
        }
        return instance;
    }

    /**
     * 执行数据库操作，自动管理SqlSession生命周期
     */
    private <T> T executeWithSession(Function<SqlSession, T> operation) {
        SqlSession sqlSession = null;
        try {
            sqlSession = myBatisPlusConfig.getSqlSession();
            return operation.apply(sqlSession);
        } finally {
            if (sqlSession != null) {
                sqlSession.close();
            }
        }
    }

    /**
     * 批量插入基础字段数据
     *
     * @param basicFields 基础字段列表
     * @return 插入的记录数，失败返回-1
     */
    public int batchInsertBasicFields(List<DeviceDetectionBasicField> basicFields) {
        if (CollectionUtils.isEmpty(basicFields)) {
            logger.debug("基础字段列表为空，跳过插入");
            return 0;
        }

        // 如果数据量较大，使用分批插入
        if (basicFields.size() > BATCH_SIZE) {
            return batchInsertBasicFieldsInChunks(basicFields);
        }

        try {
            // 使用自动管理连接的方式进行批量插入
            int insertCount = executeWithSession(sqlSession -> {
                DeviceDetectionBasicFieldMapper mapper = sqlSession.getMapper(DeviceDetectionBasicFieldMapper.class);
                return mapper.batchInsert(basicFields);
            });

            logger.info("成功批量插入基础字段数据， 插入数量: {}", insertCount);
            return insertCount;

        } catch (Exception e) {
            logger.error("批量插入基础字段数据失败", e);
            // 如果批量插入失败，尝试分批插入
            logger.info("尝试分批插入基础字段数据...");
            return batchInsertBasicFieldsInChunks(basicFields);
        }
    }

    /**
     * 分批批量插入基础字段数据
     *
     * @param basicFields 基础字段列表
     * @return 插入的记录数，失败返回-1
     */
    private int batchInsertBasicFieldsInChunks(List<DeviceDetectionBasicField> basicFields) {
        return BatchInsertUtil.processBatches(
            basicFields,
            BATCH_SIZE,
            chunk -> executeWithSession(sqlSession -> {
                DeviceDetectionBasicFieldMapper mapper = sqlSession.getMapper(DeviceDetectionBasicFieldMapper.class);
                return mapper.batchInsert(chunk);
            }),
            "基础字段数据"
        );
    }

    /**
     * 批量插入表头数据
     *
     * @param tableHeaders 表头列表
     * @return 插入的记录数，失败返回-1
     */
    public int batchInsertTableHeaders(List<DeviceDetectionTableHeader> tableHeaders) {
        if (CollectionUtils.isEmpty(tableHeaders)) {
            logger.debug("表头列表为空，跳过插入");
            return 0;
        }

        // 如果数据量较大，使用分批插入
        if (tableHeaders.size() > BATCH_SIZE) {
            return batchInsertTableHeadersInChunks(tableHeaders);
        }

        try {
            // 使用自动管理连接的方式进行批量插入
            int insertCount = executeWithSession(sqlSession -> {
                DeviceDetectionTableHeaderMapper mapper = sqlSession.getMapper(DeviceDetectionTableHeaderMapper.class);
                return mapper.batchInsert(tableHeaders);
            });

            logger.info("成功批量插入表头数据， 插入数量: {}", insertCount);
            return insertCount;

        } catch (Exception e) {
            logger.error("批量插入表头数据失败", e);
            // 如果批量插入失败，尝试分批插入
            logger.info("尝试分批插入表头数据...");
            return batchInsertTableHeadersInChunks(tableHeaders);
        }
    }

    /**
     * 分批批量插入表头数据
     *
     * @param tableHeaders 表头列表
     * @return 插入的记录数，失败返回-1
     */
    private int batchInsertTableHeadersInChunks(List<DeviceDetectionTableHeader> tableHeaders) {
        return BatchInsertUtil.processBatches(
            tableHeaders,
            BATCH_SIZE,
            chunk -> executeWithSession(sqlSession -> {
                DeviceDetectionTableHeaderMapper mapper = sqlSession.getMapper(DeviceDetectionTableHeaderMapper.class);
                return mapper.batchInsert(chunk);
            }),
            "表头数据"
        );
    }

    /**
     * 批量插入表格数据
     *
     * @param tableDataList 表格数据列表
     * @return 插入的记录数，失败返回-1
     */
    public int batchInsertTableData(List<DeviceDetectionTableData> tableDataList) {
        if (CollectionUtils.isEmpty(tableDataList)) {
            logger.debug("表格数据列表为空，跳过插入");
            return 0;
        }

        // 如果数据量较大，使用分批插入
        if (tableDataList.size() > BATCH_SIZE) {
            return batchInsertTableDataInChunks(tableDataList);
        }

        try {
            // 使用自动管理连接的方式进行批量插入
            int insertCount = executeWithSession(sqlSession -> {
                DeviceDetectionTableDataMapper mapper = sqlSession.getMapper(DeviceDetectionTableDataMapper.class);
                return mapper.batchInsert(tableDataList);
            });

            logger.info("成功批量插入表格数据，插入数量: {}", insertCount);
            return insertCount;

        } catch (Exception e) {
            logger.error("批量插入表格数据失败", e);
            // 如果批量插入失败，尝试分批插入
            logger.info("尝试分批插入表格数据...");
            return batchInsertTableDataInChunks(tableDataList);
        }
    }

    /**
     * 分批批量插入表格数据
     *
     * @param tableDataList 表格数据列表
     * @return 插入的记录数，失败返回-1
     */
    private int batchInsertTableDataInChunks(List<DeviceDetectionTableData> tableDataList) {
        return BatchInsertUtil.processBatches(
            tableDataList,
            BATCH_SIZE,
            chunk -> executeWithSession(sqlSession -> {
                DeviceDetectionTableDataMapper mapper = sqlSession.getMapper(DeviceDetectionTableDataMapper.class);
                return mapper.batchInsert(chunk);
            }),
            "表格数据"
        );
    }

    /**
     * 批量插入所有类型的数据（一次性操作）
     *
     * @param basicFields 基础字段列表
     * @param tableHeaders 表头列表
     * @param tableDataList 表格数据列表
     * @return 批量插入结果
     */
    public BatchInsertResult batchInsertAll(
                                          List<DeviceDetectionBasicField> basicFields,
                                          List<DeviceDetectionTableHeader> tableHeaders,
                                          List<DeviceDetectionTableData> tableDataList) {

        BatchInsertResult result = new BatchInsertResult();
        result.setStartTime(LocalDateTime.now());

        try {
            // 批量插入基础字段
            int basicFieldCount = batchInsertBasicFields(basicFields);
            result.setBasicFieldCount(basicFieldCount);

            // 批量插入表头
            int tableHeaderCount = batchInsertTableHeaders(tableHeaders);
            result.setTableHeaderCount(tableHeaderCount);

            // 批量插入表格数据
            int tableDataCount = batchInsertTableData(tableDataList);
            result.setTableDataCount(tableDataCount);

            // 判断是否全部成功
            boolean success = basicFieldCount >= 0 && tableHeaderCount >= 0 && tableDataCount >= 0;
            result.setSuccess(success);

            result.setEndTime(LocalDateTime.now());
            result.setTotalCount(basicFieldCount + tableHeaderCount + tableDataCount);

            if (success) {
                logger.info("批量插入全部完成， 总插入数量: {}", result.getTotalCount());
            } else {
                logger.error("批量插入部分失败");
            }

            return result;

        } catch (Exception e) {
            logger.error("批量插入全部数据失败", e);
            result.setSuccess(false);
            result.setEndTime(LocalDateTime.now());
            result.setErrorMessage(e.getMessage());
            return result;
        }
    }

    /**
     * 批量插入结果类
     */
    public static class BatchInsertResult {
        private Long detectionDataId;
        private boolean success;
        private int basicFieldCount;
        private int tableHeaderCount;
        private int tableDataCount;
        private int totalCount;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private String errorMessage;

        // Getters and Setters
        public Long getDetectionDataId() { return detectionDataId; }
        public void setDetectionDataId(Long detectionDataId) { this.detectionDataId = detectionDataId; }

        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public int getBasicFieldCount() { return basicFieldCount; }
        public void setBasicFieldCount(int basicFieldCount) { this.basicFieldCount = basicFieldCount; }

        public int getTableHeaderCount() { return tableHeaderCount; }
        public void setTableHeaderCount(int tableHeaderCount) { this.tableHeaderCount = tableHeaderCount; }

        public int getTableDataCount() { return tableDataCount; }
        public void setTableDataCount(int tableDataCount) { this.tableDataCount = tableDataCount; }

        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }

        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }

        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        /**
         * 获取执行耗时（毫秒）
         */
        public long getDurationMillis() {
            if (startTime != null && endTime != null) {
                return java.time.Duration.between(startTime, endTime).toMillis();
            }
            return 0;
        }

        @Override
        public String toString() {
            return String.format("BatchInsertResult{detectionDataId=%d, success=%s, totalCount=%d, duration=%dms}",
                detectionDataId, success, totalCount, getDurationMillis());
        }
    }

}
