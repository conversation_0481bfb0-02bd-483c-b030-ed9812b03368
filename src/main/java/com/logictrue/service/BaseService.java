package com.logictrue.service;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.config.MyBatisPlusConfig;
import org.apache.ibatis.session.SqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.function.Function;

/**
 * 基础Service类，提供批量插入功能
 *
 * @param <T> 实体类型
 * @param <M> Mapper类型
 */
public abstract class BaseService<T, M extends BaseMapper<T>> {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    protected MyBatisPlusConfig myBatisPlusConfig;
    protected M mapper;

    /**
     * 获取Mapper实例
     */
    protected abstract M getMapper(SqlSession sqlSession);

    /**
     * 初始化Service
     */
    protected void initService() {
        this.myBatisPlusConfig = MyBatisPlusConfig.getInstance();
    }

    /**
     * 执行数据库操作，自动管理SqlSession生命周期
     */
    protected <R> R executeWithSession(Function<M, R> operation) {
        SqlSession sqlSession = null;
        try {
            sqlSession = myBatisPlusConfig.getSqlSession();
            M mapper = getMapper(sqlSession);
            return operation.apply(mapper);
        } finally {
            if (sqlSession != null) {
                sqlSession.close();
            }
        }
    }

    /**
     * 批量插入数据
     * 使用MyBatis的批量执行器来提高性能
     *
     * @param entityList 实体列表
     * @param batchSize 批次大小，默认1000
     * @return 插入成功的数量
     */
    public int batchInsert(List<T> entityList, int batchSize) {
        if (entityList == null || entityList.isEmpty()) {
            logger.warn("实体列表为空，跳过批量插入");
            return 0;
        }

        int totalInserted = 0;
        int totalSize = entityList.size();

        try {
            // 使用批量执行器
            SqlSession batchSqlSession = myBatisPlusConfig.getSqlSessionFactory()
                    .openSession(org.apache.ibatis.session.ExecutorType.BATCH, false);

            try {
                M batchMapper = getMapper(batchSqlSession);

                for (int i = 0; i < totalSize; i++) {
                    batchMapper.insert(entityList.get(i));

                    // 每达到批次大小或最后一批时执行
                    if ((i + 1) % batchSize == 0 || i == totalSize - 1) {
                        batchSqlSession.flushStatements();
                        totalInserted = i + 1;
                        logger.debug("批量插入进度: {}/{}", totalInserted, totalSize);
                    }
                }

                batchSqlSession.commit();
                logger.info("批量插入完成，总数量: {}", totalInserted);

            } finally {
                batchSqlSession.close();
            }

        } catch (Exception e) {
            logger.error("批量插入失败", e);
            throw new RuntimeException("批量插入失败", e);
        }

        return totalInserted;
    }

    /**
     * 批量插入数据（使用默认批次大小1000）
     *
     * @param entityList 实体列表
     * @return 插入成功的数量
     */
    public int batchInsert(List<T> entityList) {
        return batchInsert(entityList, 1000);
    }

    /**
     * 单条插入
     *
     * @param entity 实体
     * @return 插入结果
     */
    public int insert(T entity) {
        return executeWithSession(mapper -> mapper.insert(entity));
    }
}
