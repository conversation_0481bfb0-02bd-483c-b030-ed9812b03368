package com.logictrue.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLTransientConnectionException;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 批量插入工具类
 * 提供重试机制和连接超时处理
 */
public class BatchInsertUtil {
    private static final Logger logger = LoggerFactory.getLogger(BatchInsertUtil.class);
    
    // 默认重试次数
    private static final int DEFAULT_MAX_RETRIES = 3;
    // 重试间隔（毫秒）
    private static final long RETRY_DELAY_MS = 1000;
    
    /**
     * 执行带重试机制的批量插入操作
     *
     * @param operation 批量插入操作
     * @param dataList 数据列表
     * @param operationName 操作名称（用于日志）
     * @param <T> 数据类型
     * @return 插入的记录数，失败返回-1
     */
    public static <T> int executeWithRetry(Function<List<T>, Integer> operation, 
                                          List<T> dataList, 
                                          String operationName) {
        return executeWithRetry(operation, dataList, operationName, DEFAULT_MAX_RETRIES);
    }
    
    /**
     * 执行带重试机制的批量插入操作
     *
     * @param operation 批量插入操作
     * @param dataList 数据列表
     * @param operationName 操作名称（用于日志）
     * @param maxRetries 最大重试次数
     * @param <T> 数据类型
     * @return 插入的记录数，失败返回-1
     */
    public static <T> int executeWithRetry(Function<List<T>, Integer> operation, 
                                          List<T> dataList, 
                                          String operationName,
                                          int maxRetries) {
        int retryCount = 0;
        
        while (retryCount <= maxRetries) {
            try {
                int result = operation.apply(dataList);
                if (retryCount > 0) {
                    logger.info("{}重试成功，重试次数: {}", operationName, retryCount);
                }
                return result;
                
            } catch (Exception e) {
                retryCount++;
                
                // 检查是否是连接超时异常
                if (isConnectionTimeoutException(e)) {
                    if (retryCount <= maxRetries) {
                        logger.warn("{}连接超时，第{}次重试 (最大{}次): {}", 
                                   operationName, retryCount, maxRetries, e.getMessage());
                        
                        // 等待一段时间后重试
                        try {
                            TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS * retryCount);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            logger.error("重试等待被中断", ie);
                            return -1;
                        }
                    } else {
                        logger.error("{}连接超时，已达到最大重试次数: {}", operationName, maxRetries, e);
                        return -1;
                    }
                } else {
                    // 非连接超时异常，直接失败
                    logger.error("{}执行失败，非连接超时异常", operationName, e);
                    return -1;
                }
            }
        }
        
        return -1;
    }
    
    /**
     * 检查异常是否为连接超时异常
     */
    private static boolean isConnectionTimeoutException(Throwable e) {
        if (e == null) {
            return false;
        }
        
        // 检查异常消息
        String message = e.getMessage();
        if (message != null) {
            message = message.toLowerCase();
            if (message.contains("connection is not available") ||
                message.contains("request timed out") ||
                message.contains("connection timeout") ||
                message.contains("hikaripool")) {
                return true;
            }
        }
        
        // 检查异常类型
        if (e instanceof SQLTransientConnectionException) {
            return true;
        }
        
        // 递归检查原因异常
        return isConnectionTimeoutException(e.getCause());
    }
    
    /**
     * 分批处理数据
     *
     * @param dataList 数据列表
     * @param batchSize 批次大小
     * @param processor 处理器
     * @param operationName 操作名称
     * @param <T> 数据类型
     * @return 总处理数量，失败返回-1
     */
    public static <T> int processBatches(List<T> dataList, 
                                        int batchSize, 
                                        Function<List<T>, Integer> processor,
                                        String operationName) {
        if (dataList == null || dataList.isEmpty()) {
            return 0;
        }
        
        int totalSize = dataList.size();
        int totalProcessed = 0;
        
        logger.info("开始分批处理{}，总数量: {}, 批次大小: {}", operationName, totalSize, batchSize);
        
        try {
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<T> chunk = dataList.subList(i, endIndex);
                
                int processed = executeWithRetry(processor, chunk, operationName + "分批");
                if (processed < 0) {
                    logger.error("分批处理{}失败，已处理: {}/{}", operationName, totalProcessed, totalSize);
                    return -1;
                }
                
                totalProcessed += processed;
                logger.debug("分批处理{}进度: {}/{}, 当前批次: {}", 
                           operationName, totalProcessed, totalSize, processed);
                
                // 添加短暂延迟，避免连接池压力
                if (i + batchSize < totalSize) {
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        logger.warn("分批处理延迟被中断");
                        break;
                    }
                }
            }
            
            logger.info("分批处理{}完成，总处理数量: {}", operationName, totalProcessed);
            return totalProcessed;
            
        } catch (Exception e) {
            logger.error("分批处理{}异常，已处理: {}/{}", operationName, totalProcessed, totalSize, e);
            return -1;
        }
    }
}
