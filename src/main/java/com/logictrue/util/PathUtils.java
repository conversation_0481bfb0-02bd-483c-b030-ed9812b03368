package com.logictrue.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.net.URISyntaxException;

/**
 * 路径工具类，提供统一的路径获取方法
 * 兼容AppImage打包环境和普通jar包环境
 */
public class PathUtils {
    private static final Logger logger = LoggerFactory.getLogger(PathUtils.class);
    
    /**
     * 获取应用程序目录
     * 兼容AppImage环境，当在AppImage或只读环境中运行时，使用用户主目录下的配置目录
     */
    public static String getApplicationDirectory() {
        try {
            // 获取当前运行的jar文件路径
            String jarPath = PathUtils.class.getProtectionDomain()
                    .getCodeSource()
                    .getLocation()
                    .toURI()
                    .getPath();

            File jarFile = new File(jarPath);

            // 检查是否在AppImage环境中运行
            if (isRunningInAppImage(jarPath)) {
                // AppImage环境：使用用户主目录下的应用配置目录
                String userHome = System.getProperty("user.home");
                String appConfigDir = userHome + File.separator + ".iotclient";
                logger.debug("应用程序目录 (AppImage模式): {}", appConfigDir);
                return appConfigDir;
            }

            // 如果是jar文件，检查是否可写
            if (jarFile.isFile() && jarPath.endsWith(".jar")) {
                String appDir = jarFile.getParent();
                File testDir = new File(appDir);

                // 检查目录是否可写
                if (testDir.canWrite()) {
                    logger.debug("应用程序目录 (jar模式): {}", appDir);
                    return appDir;
                } else {
                    // 如果jar所在目录不可写，使用用户主目录
                    String userHome = System.getProperty("user.home");
                    String appConfigDir = userHome + File.separator + ".iotclient";
                    logger.debug("应用程序目录 (jar模式-只读，使用用户目录): {}", appConfigDir);
                    return appConfigDir;
                }
            }

            // 如果是在IDE中运行，返回项目根目录
            String userDir = System.getProperty("user.dir");
            logger.debug("应用程序目录 (开发模式): {}", userDir);
            return userDir;

        } catch (URISyntaxException e) {
            logger.warn("无法获取应用程序目录，使用用户主目录", e);
            String userHome = System.getProperty("user.home");
            return userHome + File.separator + ".iotclient";
        }
    }

    /**
     * 获取jar文件所在目录
     * 简化版本，主要用于向后兼容
     */
    public static String getJarDirectory() {
        try {
            // 优先使用系统属性中设置的数据目录
            String dataDir = System.getProperty("iot.data.dir");
            if (dataDir != null && !dataDir.isEmpty()) {
                return dataDir;
            }

            // 如果系统属性未设置，使用应用程序目录
            return getApplicationDirectory();
        } catch (Exception e) {
            logger.warn("获取jar目录失败，使用当前工作目录", e);
            return System.getProperty("user.dir");
        }
    }

    /**
     * 检查是否在AppImage环境中运行
     */
    public static boolean isRunningInAppImage(String jarPath) {
        // AppImage会将应用挂载到/tmp/.mount_xxx目录
        return jarPath.startsWith("/tmp/.mount_") ||
               System.getenv("APPIMAGE") != null ||
               System.getenv("APPDIR") != null;
    }

    /**
     * 检查是否在AppImage环境中运行（无参数版本）
     */
    public static boolean isRunningInAppImage() {
        try {
            String jarPath = PathUtils.class.getProtectionDomain()
                    .getCodeSource()
                    .getLocation()
                    .toURI()
                    .getPath();
            return isRunningInAppImage(jarPath);
        } catch (URISyntaxException e) {
            logger.warn("检查AppImage环境失败", e);
            return System.getenv("APPIMAGE") != null || System.getenv("APPDIR") != null;
        }
    }

    /**
     * 确保目录存在，如果不存在则创建
     */
    public static boolean ensureDirectoryExists(String directoryPath) {
        try {
            File directory = new File(directoryPath);
            if (!directory.exists()) {
                boolean created = directory.mkdirs();
                if (created) {
                    logger.debug("创建目录: {}", directoryPath);
                } else {
                    logger.warn("创建目录失败: {}", directoryPath);
                }
                return created;
            }
            return true;
        } catch (Exception e) {
            logger.error("确保目录存在时发生异常: {}", directoryPath, e);
            return false;
        }
    }
}
