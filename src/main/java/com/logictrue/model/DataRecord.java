package com.logictrue.model;

import javafx.beans.property.*;

import java.time.LocalDateTime;

/**
 * 数据记录模型类
 * 用于表示数据详情表格中的数据记录
 */
public class DataRecord {
    private final IntegerProperty id;
    private final StringProperty fileName;
    private final ObjectProperty<LocalDateTime> collectTime;
    private final StringProperty filePath;

    /**
     * 默认构造函数
     */
    public DataRecord() {
        this.id = new SimpleIntegerProperty();
        this.fileName = new SimpleStringProperty();
        this.collectTime = new SimpleObjectProperty<>();
        this.filePath = new SimpleStringProperty();
    }

    /**
     * 带参数的构造函数
     */
    public DataRecord(int id, String fileName, LocalDateTime collectTime, String filePath) {
        this.id = new SimpleIntegerProperty(id);
        this.fileName = new SimpleStringProperty(fileName);
        this.collectTime = new SimpleObjectProperty<>(collectTime);
        this.filePath = new SimpleStringProperty(filePath);
    }

    // ID属性的getter和setter
    public int getId() {
        return id.get();
    }

    public void setId(int id) {
        this.id.set(id);
    }

    public IntegerProperty idProperty() {
        return id;
    }

    // 文件名属性的getter和setter
    public String getFileName() {
        return fileName.get();
    }

    public void setFileName(String fileName) {
        this.fileName.set(fileName);
    }

    public StringProperty fileNameProperty() {
        return fileName;
    }

    // 采集时间属性的getter和setter
    public LocalDateTime getCollectTime() {
        return collectTime.get();
    }

    public void setCollectTime(LocalDateTime collectTime) {
        this.collectTime.set(collectTime);
    }

    public ObjectProperty<LocalDateTime> collectTimeProperty() {
        return collectTime;
    }

    // 文件路径属性的getter和setter
    public String getFilePath() {
        return filePath.get();
    }

    public void setFilePath(String filePath) {
        this.filePath.set(filePath);
    }

    public StringProperty filePathProperty() {
        return filePath;
    }

    @Override
    public String toString() {
        return "DataRecord{" +
                "id=" + getId() +
                ", fileName='" + getFileName() + '\'' +
                ", collectTime=" + getCollectTime() +
                ", filePath='" + getFilePath() + '\'' +
                '}';
    }
}
