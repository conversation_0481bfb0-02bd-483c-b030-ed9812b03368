package com.logictrue.controller;

import com.logictrue.config.ConfigManager;
import com.logictrue.model.FormField;
import com.logictrue.service.NetworkService;
import com.logictrue.util.FormLayoutUtil;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * 动态表单控制器
 */
public class DynamicFormController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(DynamicFormController.class);

    @FXML
    private Label formTitleLabel;

    @FXML
    private ScrollPane formScrollPane;

    @FXML
    private VBox formContainer;

    @FXML
    private Button backButton;

    @FXML
    private ProgressIndicator progressIndicator;

    private ConfigManager configManager;
    private Map<String, Control> fieldControls = new HashMap<>();

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        configManager = ConfigManager.getInstance();

        // 初始化界面
        initializeUI();

        // 加载表单配置
        loadFormConfig();

        // 生成动态表单
        generateDynamicForm();

        logger.info("动态表单界面初始化完成");
    }

    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 隐藏进度指示器
        progressIndicator.setVisible(false);

        // 绑定事件
        backButton.setOnAction(event -> closeWindow());

        // 设置表单容器
        formContainer.setSpacing(15);
        formContainer.setPadding(new Insets(20));
    }

    /**
     * 加载表单配置
     */
    private void loadFormConfig() {
        // 设置表单标题
        formTitleLabel.setText(configManager.getFormName());
    }

    /**
     * 生成动态表单
     */
    private void generateDynamicForm() {
        List<FormField> formFields = configManager.getFormFields();

        GridPane formGrid = new GridPane();
        formGrid.setHgap(15);
        formGrid.setVgap(15);

        int row = 0;
        int col = 0;
        final int maxColumns = 4; // 每行最多4个字段
        int textAreaCount = 0; // 统计文本区域数量

        for (FormField field : formFields) {
            // 创建标签
            Label label = new Label(field.getLabel() + ":");
            label.setMinWidth(100);

            // 创建输入控件
            Control inputControl = createInputControl(field);
            fieldControls.put(field.getName(), inputControl);

            // 添加到网格
            formGrid.add(label, col * 2, row);
            formGrid.add(inputControl, col * 2 + 1, row);

            // 更新位置
            col++;
            if (col >= maxColumns || field.getType() == FormField.FieldType.TEXTAREA) {
                col = 0;
                row++;

                // 如果是文本区域，占满整行并统计数量
                if (field.getType() == FormField.FieldType.TEXTAREA) {
                    GridPane.setColumnSpan(label, 1);
                    GridPane.setColumnSpan(inputControl, maxColumns * 2 - 1);
                    textAreaCount++;
                }
            }
        }

        formContainer.getChildren().clear();
        formContainer.getChildren().add(formGrid);

        // 根据字段数量动态调整表单高度
        FormLayoutUtil.FormLayoutInfo layoutInfo = new FormLayoutUtil.FormLayoutInfo(
            formFields.size(), textAreaCount, row);
        FormLayoutUtil.adjustDialogFormHeight(formScrollPane, layoutInfo);

    }

    /**
     * 创建输入控件
     */
    private Control createInputControl(FormField field) {
        Control control;

        switch (field.getType()) {
            case TEXTAREA:
                TextArea textArea = new TextArea();
                textArea.setPromptText("请输入" + field.getLabel());
                textArea.setPrefRowCount(3);
                textArea.setWrapText(true);
                control = textArea;
                break;

            case NUMBER:
                TextField numberField = new TextField();
                numberField.setPromptText("请输入" + field.getLabel());
                // 添加数字验证
                numberField.textProperty().addListener((observable, oldValue, newValue) -> {
                    if (!newValue.matches("\\d*\\.?\\d*")) {
                        numberField.setText(oldValue);
                    }
                });
                control = numberField;
                break;

            case DATE:
                DatePicker datePicker = new DatePicker();
                datePicker.setPromptText("请选择" + field.getLabel());
                control = datePicker;
                break;

            case TEXT:
            default:
                TextField textField = new TextField();
                textField.setPromptText("请输入" + field.getLabel());
                control = textField;
                break;
        }

        return control;
    }


    /**
     * 关闭窗口
     */
    @FXML
    private void closeWindow() {
        Stage stage = (Stage) backButton.getScene().getWindow();
        stage.close();
    }

}
