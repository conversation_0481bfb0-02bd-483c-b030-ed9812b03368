package com.logictrue.controller;

import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.service.DatabaseService;
import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

/**
 * Excel数据采集记录详情控制器
 * 展示device_detection_data表的内容
 */
public class DataListController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(DataListController.class);

    @FXML
    private Button backButton;

    @FXML
    private Button refreshButton;

    @FXML
    private TextField fileNameSearchField;

    @FXML
    private ComboBox<String> parseStatusComboBox;

    @FXML
    private TextField deviceCodeSearchField;

    @FXML
    private Button searchButton;

    @FXML
    private Button resetButton;

    @FXML
    private TableView<DeviceDetectionData> dataTable;

    @FXML
    private TableColumn<DeviceDetectionData, Long> idColumn;

    @FXML
    private TableColumn<DeviceDetectionData, String> deviceCodeColumn;

    @FXML
    private TableColumn<DeviceDetectionData, String> templateNameColumn;

    @FXML
    private TableColumn<DeviceDetectionData, String> fileNameColumn;

    @FXML
    private TableColumn<DeviceDetectionData, String> parseStatusColumn;

    @FXML
    private TableColumn<DeviceDetectionData, String> parseTimeColumn;

    @FXML
    private TableColumn<DeviceDetectionData, Integer> totalSheetsColumn;

    @FXML
    private TableColumn<DeviceDetectionData, Integer> parsedSheetsColumn;

    @FXML
    private TableColumn<DeviceDetectionData, Integer> basicFieldsCountColumn;

    @FXML
    private TableColumn<DeviceDetectionData, Integer> tableRowsCountColumn;

    @FXML
    private TableColumn<DeviceDetectionData, String> createTimeColumn;

    @FXML
    private TableColumn<DeviceDetectionData, Void> actionColumn;

    @FXML
    private Button firstPageButton;

    @FXML
    private Button prevPageButton;

    @FXML
    private Button nextPageButton;

    @FXML
    private Button lastPageButton;

    @FXML
    private Label pageInfoLabel;

    @FXML
    private ComboBox<String> pageSizeComboBox;

    @FXML
    private Label totalCountLabel;

    @FXML
    private ProgressIndicator progressIndicator;

    @FXML
    private Label statusLabel;

    private DatabaseService databaseService;
    private ObservableList<DeviceDetectionData> dataList;
    private int currentPage = 1;
    private int pageSize = 20;
    private int totalCount = 0;
    private int totalPages = 0;

    private MainController mainController;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        databaseService = DatabaseService.getInstance();
        dataList = FXCollections.observableArrayList();

        initializeTable();
        initializePagination();
        initializeSearchControls();
        initializeEventHandlers();
        loadData();

        logger.info("Excel数据采集记录详情控制器初始化完成");
    }

    /**
     * 初始化表格
     */
    private void initializeTable() {
        // 设置ID列
        idColumn.setCellValueFactory(new PropertyValueFactory<>("id"));
        idColumn.setCellFactory(column -> createCenteredCell());

        // 设置设备编码列
        deviceCodeColumn.setCellValueFactory(new PropertyValueFactory<>("deviceCode"));
        deviceCodeColumn.setCellFactory(column -> createCenteredCell());

        // 设置模板名称列
        templateNameColumn.setCellValueFactory(new PropertyValueFactory<>("templateName"));
        templateNameColumn.setCellFactory(column -> createCenteredCell());

        // 设置文件名列
        fileNameColumn.setCellValueFactory(new PropertyValueFactory<>("fileName"));
        fileNameColumn.setCellFactory(column -> createCenteredCell());

        // 设置解析状态列
        parseStatusColumn.setCellValueFactory(cellData -> {
            String statusText = getParseStatusText(cellData.getValue().getParseStatus());
            return new SimpleStringProperty(statusText);
        });
        parseStatusColumn.setCellFactory(column -> createCenteredCell());

        // 设置解析时间列
        parseTimeColumn.setCellValueFactory(cellData -> {
            LocalDateTime parseTime = cellData.getValue().getParseTime();
            String formattedTime = parseTime != null ?
                parseTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "";
            return new SimpleStringProperty(formattedTime);
        });
        parseTimeColumn.setCellFactory(column -> createCenteredCell());

        // 设置总Sheet数列
        totalSheetsColumn.setCellValueFactory(new PropertyValueFactory<>("totalSheets"));
        totalSheetsColumn.setCellFactory(column -> createCenteredCell());

        // 设置已解析Sheet数列
        parsedSheetsColumn.setCellValueFactory(new PropertyValueFactory<>("parsedSheets"));
        parsedSheetsColumn.setCellFactory(column -> createCenteredCell());

        // 设置基础字段数列
        basicFieldsCountColumn.setCellValueFactory(new PropertyValueFactory<>("basicFieldsCount"));
        basicFieldsCountColumn.setCellFactory(column -> createCenteredCell());

        // 设置表格行数列
        tableRowsCountColumn.setCellValueFactory(new PropertyValueFactory<>("tableRowsCount"));
        tableRowsCountColumn.setCellFactory(column -> createCenteredCell());

        // 设置创建时间列
        createTimeColumn.setCellValueFactory(cellData -> {
            LocalDateTime createTime = cellData.getValue().getCreateTime();
            String formattedTime = createTime != null ?
                createTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "";
            return new SimpleStringProperty(formattedTime);
        });
        createTimeColumn.setCellFactory(column -> createCenteredCell());

        // 设置操作列
        actionColumn.setCellFactory(param -> new TableCell<DeviceDetectionData, Void>() {
            private final Button detailButton = new Button("详情");

            {
                detailButton.setOnAction(event -> {
                    DeviceDetectionData record = getTableView().getItems().get(getIndex());
                    showRecordDetail(record);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(detailButton);
                }
                setAlignment(Pos.CENTER);
            }
        });

        dataTable.setItems(dataList);
    }

    /**
     * 获取解析状态文本
     */
    private String getParseStatusText(Integer parseStatus) {
        if (parseStatus == null) return "未知";
        switch (parseStatus) {
            case 0:
                return "待解析";
            case 1:
                return "解析成功";
            case 2:
                return "解析失败";
            default:
                return "未知";
        }
    }

    /**
     * 创建居中对齐的表格单元格
     */
    private <T> TableCell<DeviceDetectionData, T> createCenteredCell() {
        return new TableCell<DeviceDetectionData, T>() {
            @Override
            protected void updateItem(T item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.toString());
                }
                setAlignment(Pos.CENTER);
            }
        };
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 初始化搜索控件
     */
    private void initializeSearchControls() {
        // 初始化解析状态下拉框
        parseStatusComboBox.getItems().addAll("全部", "待解析", "解析成功", "解析失败");
        parseStatusComboBox.setValue("全部");
    }

    /**
     * 初始化事件处理器
     */
    private void initializeEventHandlers() {
        // 搜索按钮事件
        searchButton.setOnAction(event -> performSearch());

        // 重置按钮事件
        resetButton.setOnAction(event -> resetSearch());

        // 刷新按钮事件
        refreshButton.setOnAction(event -> loadData());

        // 返回按钮事件
        backButton.setOnAction(event -> goBack());
    }

    /**
     * 初始化分页控件
     */
    private void initializePagination() {
        // 设置页面大小下拉框选项
        pageSizeComboBox.getItems().addAll("10", "20", "50", "100");
        pageSizeComboBox.setValue("20");
        pageSizeComboBox.setOnAction(event -> {
            String selectedSize = pageSizeComboBox.getValue();
            if (selectedSize != null) {
                pageSize = Integer.parseInt(selectedSize);
                currentPage = 1;
                loadData();
            }
        });

        // 绑定分页按钮事件
        firstPageButton.setOnAction(event -> goToFirstPage());
        prevPageButton.setOnAction(event -> goToPrevPage());
        nextPageButton.setOnAction(event -> goToNextPage());
        lastPageButton.setOnAction(event -> goToLastPage());
    }

    /**
     * 加载数据
     */
    private void loadData() {
        showStatus("正在加载数据...", true);

        Task<List<DeviceDetectionData>> loadTask = new Task<List<DeviceDetectionData>>() {
            @Override
            protected List<DeviceDetectionData> call() throws Exception {
                return databaseService.getExcelDataRecords(currentPage, pageSize);
            }
        };

        loadTask.setOnSucceeded(event -> {
            Platform.runLater(() -> {
                List<DeviceDetectionData> records = loadTask.getValue();
                dataList.clear();
                dataList.addAll(records);

                // 更新总记录数和分页信息
                updatePaginationInfo();

                showStatus("数据加载完成，共 " + records.size() + " 条记录", false);
                logger.info("Excel数据记录加载完成，当前页: {}, 记录数: {}", currentPage, records.size());
            });
        });

        loadTask.setOnFailed(event -> {
            Platform.runLater(() -> {
                showStatus("数据加载失败", false);
                logger.error("Excel数据记录加载失败", loadTask.getException());
                showAlert("错误", "数据加载失败: " + loadTask.getException().getMessage());
            });
        });

        Thread loadThread = new Thread(loadTask);
        loadThread.setDaemon(true);
        loadThread.start();
    }

    /**
     * 更新分页信息
     */
    private void updatePaginationInfo() {
        totalCount = databaseService.getExcelDataRecordCount();
        totalPages = (int) Math.ceil((double) totalCount / pageSize);

        if (totalPages == 0) {
            totalPages = 1;
        }

        // 更新页面信息标签
        pageInfoLabel.setText(String.format("第 %d 页，共 %d 页", currentPage, totalPages));
        totalCountLabel.setText(String.format("共 %d 条记录", totalCount));

        // 更新按钮状态
        firstPageButton.setDisable(currentPage <= 1);
        prevPageButton.setDisable(currentPage <= 1);
        nextPageButton.setDisable(currentPage >= totalPages);
        lastPageButton.setDisable(currentPage >= totalPages);
    }

    /**
     * 跳转到首页
     */
    private void goToFirstPage() {
        if (currentPage > 1) {
            currentPage = 1;
            loadData();
        }
    }

    /**
     * 跳转到上一页
     */
    private void goToPrevPage() {
        if (currentPage > 1) {
            currentPage--;
            loadData();
        }
    }

    /**
     * 跳转到下一页
     */
    private void goToNextPage() {
        if (currentPage < totalPages) {
            currentPage++;
            loadData();
        }
    }

    /**
     * 跳转到末页
     */
    private void goToLastPage() {
        if (currentPage < totalPages) {
            currentPage = totalPages;
            loadData();
        }
    }

    /**
     * 返回主页面
     */
    private void goBack() {
        if (mainController != null) {
            mainController.showMainPage();
        }
    }

    /**
     * 执行搜索
     */
    private void performSearch() {
        currentPage = 1;
        loadData();
    }

    /**
     * 重置搜索
     */
    private void resetSearch() {
        fileNameSearchField.clear();
        deviceCodeSearchField.clear();
        parseStatusComboBox.setValue("全部");
        currentPage = 1;
        loadData();
    }

    /**
     * 显示状态信息
     */
    private void showStatus(String message, boolean showProgress) {
        statusLabel.setText(message);
        progressIndicator.setVisible(showProgress);
    }

    /**
     * 显示记录详情
     */
    private void showRecordDetail(DeviceDetectionData record) {
        showStatus("正在加载详情数据...", true);

        Task<DatabaseService.DeviceDetectionDataDetail> loadDetailTask = new Task<DatabaseService.DeviceDetectionDataDetail>() {
            @Override
            protected DatabaseService.DeviceDetectionDataDetail call() throws Exception {
                return databaseService.getExcelDataDetail(record.getId());
            }
        };

        loadDetailTask.setOnSucceeded(event -> {
            Platform.runLater(() -> {
                showStatus("就绪", false);
                DatabaseService.DeviceDetectionDataDetail detail = loadDetailTask.getValue();

                if (detail != null) {
                    showDetailDialog(detail);
                } else {
                    showAlert("错误", "无法加载详情数据");
                }
            });
        });

        loadDetailTask.setOnFailed(event -> {
            Platform.runLater(() -> {
                showStatus("加载详情失败", false);
                logger.error("加载Excel数据详情失败", loadDetailTask.getException());
                showAlert("错误", "加载详情数据失败: " + loadDetailTask.getException().getMessage());
            });
        });

        Thread loadThread = new Thread(loadDetailTask);
        loadThread.setDaemon(true);
        loadThread.start();
    }

    /**
     * 显示详情对话框
     */
    private void showDetailDialog(DatabaseService.DeviceDetectionDataDetail detail) {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(
                    getClass().getResource("/fxml/excel-data-detail-dialog.fxml"));
            javafx.scene.Parent root = loader.load();

            ExcelDataDetailDialogController controller = loader.getController();
            controller.setDataDetail(detail);

            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("设备检测数据详情 - " + detail.getMainRecord().getFileName());
            stage.setScene(new javafx.scene.Scene(root, 1000, 700));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.setResizable(true);

            // 设置图标（如果有的话）
            // stage.getIcons().add(new Image(getClass().getResourceAsStream("/icons/detail.png")));

            stage.showAndWait();

        } catch (Exception e) {
            logger.error("显示详情对话框失败", e);
            showAlert("错误", "显示详情对话框失败: " + e.getMessage());
        }
    }

    /**
     * 设置主控制器
     */
    public void setMainController(MainController mainController) {
        this.mainController = mainController;
    }

    /**
     * 显示警告对话框
     */
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);

        // 设置对话框大小以适应长文本
        alert.getDialogPane().setPrefWidth(600);
        alert.getDialogPane().setPrefHeight(400);

        alert.showAndWait();
    }
}
