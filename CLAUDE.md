# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a JavaFX-based IoT data collection application built with Java 21. The application provides device management, dynamic form generation, data collection, heartbeat monitoring, and external application integration capabilities.

## Build and Development Commands

### Core Commands
```bash
# Build the project
./gradlew build

# Run the application
./gradlew run

# Run tests
./gradlew test

# Create executable JAR
./gradlew shadowJar

# Create native packages
./gradlew jpackage
```

### Distribution Commands
```bash
# Create all distribution formats (app-image, deb, AppImage)
./gradlew packageAll

# Create specific package types
./gradlew packageAppImage    # Linux AppImage
./gradlew packageDeb         # Debian package
./gradlew packageRpm         # RPM package

# Create AppImage manually
./gradlew createAppImage
```

### Testing Commands
```bash
# Run specific test class
./run-test.sh

# Run with Gradle
./gradlew test --tests com.logictrue.DataDetailTest
```

## Architecture Overview

### Core Components

**Application Entry Point**
- `App.java:25` - Main application class, handles JavaFX initialization and fullscreen setup
- Starts in fullscreen mode by default with no exit hint

**Main Controller** (`src/main/java/com/logictrue/controller/MainController.java`)
- Central controller managing the main interface with multiple pages (main, form, data detail)
- Handles dynamic form generation, background image management, and external app integration
- Manages fullscreen mode transitions and button states

**Configuration System**
- `ConfigManager.java` - Singleton pattern for managing application configuration
- Configuration stored in `config.json` in project root
- Handles device settings, form fields, external apps, and API endpoints

**Database Layer**
- `DatabaseService.java:36` - SQLite database service for data records
- Singleton pattern with automatic database initialization
- Provides CRUD operations for collected data with pagination support

**Network Services**
- `NetworkService.java` - Handles HTTP communication for form submission
- `HeartbeatService.java` - Periodic heartbeat monitoring (30s intervals, 5s timeout)
- `ExternalAppService.java` - Manages launching external applications

**Model Classes**
- `DataRecord.java` - Represents collected data records
- `FormField.java` - Defines form field types and properties
- `ExternalApp.java` - External application configuration

### Page Structure

The application uses a multi-page interface:
1. **Main Page** - Background image, quick start button, settings, external app buttons
2. **Form Page** - Dynamically generated form based on configuration
3. **Data Detail Page** - Displays collected data records with pagination

### Key Features

**Dynamic Form System**
- Form fields defined in configuration (`formFields` array in config.json)
- Supports TEXT, NUMBER, DATE, and TEXTAREA field types
- Automatic layout generation with 2-column grid layout
- Real-time validation and data type conversion

**External Application Integration**
- Configurable external applications in `externalApps` array
- Supports AppImage executables and other application types
- Buttons dynamically generated on main page

**Background Image Management**
- Supports custom background images via configuration
- Automatic image download from configured API endpoints
- Fallback to default background image

**Fullscreen Mode**
- Application starts in fullscreen mode by default
- Dedicated fullscreen controls with SVG icons
- Background image adaptation to fullscreen resolution

## Configuration Structure

The application uses `config.json` for configuration:

```json
{
  "deviceId": "Device identifier",
  "formName": "Form display name", 
  "apiUrl": "Form submission endpoint",
  "heartbeatUrl": "Heartbeat monitoring endpoint",
  "imageUrl": "Device image download URL",
  "backgroundImagePath": "Local path to background image",
  "formFields": [/* Field definitions */],
  "externalApps": [/* External app configurations */]
}
```

## Database Schema

SQLite database (`data_records.db`) with single table:
- `id` - Primary key
- `file_name` - Collected file name
- `collect_time` - Timestamp of collection
- `file_path` - Path to collected file

## Development Notes

### Java Module System
- Uses Java modules with `module-info.java`
- Main module: `com.logictrue`
- Requires JavaFX controls and FXML modules

### Logging
- Uses SLF4J with Logback implementation
- Separate log files for application and heartbeat events
- Log files stored in `logs/` directory

### Dependencies
- Jackson for JSON processing
- SQLite JDBC for database operations
- Custom `logictrue-iot-excel` library for Excel export functionality
- JavaFX 21 for UI components

### Testing
- JUnit 5 for testing framework
- Test class: `DataDetailTest.java`
- Custom test runner script: `run-test.sh`

### Package Structure
- Follows standard Maven/Gradle directory structure
- FXML layouts in `src/main/resources/fxml/`
- CSS styles in `src/main/resources/css/`
- Images in `src/main/resources/images/`